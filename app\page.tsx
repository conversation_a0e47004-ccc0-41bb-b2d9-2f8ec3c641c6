"use client"

import { useState } from "react"
import { ChatDialog } from "@/components/ChatDialog"
import { CallDialog } from "@/components/CallDialog"
import { FloatingButtonWidget } from "@/components/FloatingButtonWidget"

export default function Home() {
  const [isChatOpen, setIsChatOpen] = useState(false)
  const [isCallOpen, setIsCallOpen] = useState(false)

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-500 to-orange-600 flex items-center justify-center p-4 relative overflow-hidden">
      {/* Animated background */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -inset-10 opacity-30">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-purple-500/30 rounded-full mix-blend-multiply filter blur-xl animate-blob" />
          <div className="absolute top-1/3 right-1/4 w-96 h-96 bg-yellow-500/30 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-2000" />
          <div className="absolute bottom-1/4 left-1/3 w-96 h-96 bg-pink-500/30 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-4000" />
        </div>
      </div>

      {/* Floating particles */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className="absolute w-2 h-2 bg-white/20 rounded-full animate-float"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 5}s`,
              animationDuration: `${3 + Math.random() * 4}s`,
            }}
          />
        ))}
      </div>

      {/* Background content */}
      <div className="text-center text-white space-y-6 max-w-4xl mx-auto">
        <h1 className="text-6xl md:text-8xl font-bold mb-8 animate-glow">AI Voice Experience</h1>
        <p className="text-xl md:text-2xl text-white/80 max-w-2xl mx-auto">
          Converse with our advanced AI assistant powered by ElevenLabs technology
        </p>

        <div className="mt-16 space-y-4 text-white/70">
          <p className="text-lg">Features:</p>
          <ul className="space-y-2">
            <li>• Beautiful animated chat interface</li>
            <li>• Professional voice call experience</li>
            <li>• Smooth transitions and effects</li>
            <li>• Mobile-responsive design</li>
          </ul>
        </div>
      </div>

      {/* Bottom text like in the image */}
      <div className="absolute bottom-4 left-4 text-white/60 text-sm">
        <div>Activate Windows</div>
        <div className="text-xs">Go to Settings to activate Windows</div>
      </div>

      {/* Floating Button Widget */}
      <FloatingButtonWidget />

      {/* Dialogs */}
      <ChatDialog isOpen={isChatOpen} onClose={() => setIsChatOpen(false)} />
      <CallDialog isOpen={isCallOpen} onClose={() => setIsCallOpen(false)} />
    </div>
  )
}
